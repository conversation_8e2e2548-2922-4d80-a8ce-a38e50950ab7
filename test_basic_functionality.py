#!/usr/bin/env python3
"""
基础功能测试脚本
测试Shader分析器的基本功能，不依赖AI模型
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_shader_parser():
    """测试Shader解析器"""
    print("🧪 测试Shader解析器...")
    
    try:
        from shader_analyzer import ShaderParser
        
        # 检查示例Shader文件
        example_file = Path("example_shaders/basic_shader.shader")
        if not example_file.exists():
            print(f"❌ 示例文件不存在: {example_file}")
            return False
        
        parser = ShaderParser()
        result = parser.parse_file(str(example_file))
        
        if result:
            print(f"✅ 解析成功: {result.get('shader_name', 'Unknown')}")
            print(f"   - 属性数量: {len(result.get('properties', []))}")
            print(f"   - 函数数量: {len(result.get('functions', []))}")
            print(f"   - Pass数量: {len(result.get('passes', []))}")
            return True
        else:
            print("❌ 解析失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_manager():
    """测试模型管理器"""
    print("\n🧪 测试模型管理器...")
    
    try:
        from model_manager import HunyuanModelManager
        
        manager = HunyuanModelManager()
        
        # 测试获取可用模型
        available_models = manager.get_available_models()
        print(f"✅ 可用模型数量: {len(available_models)}")
        
        # 测试内存信息
        memory_info = manager.get_memory_info()
        print(f"✅ 系统内存: {memory_info['system_total']:.1f}GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_download_script():
    """测试下载脚本"""
    print("\n🧪 测试下载脚本...")
    
    try:
        import subprocess
        
        # 测试列出模型
        result = subprocess.run([
            sys.executable, "download_model.py", "--list"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 下载脚本运行正常")
            return True
        else:
            print(f"❌ 下载脚本失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_basic_analysis():
    """测试基础分析功能"""
    print("\n🧪 测试基础分析功能...")
    
    try:
        from shader_analyzer import UnityShaderAnalyzer
        
        # 创建分析器（不加载AI模型）
        analyzer = UnityShaderAnalyzer(model_size="1.8B")
        
        # 检查示例文件
        example_file = Path("example_shaders/basic_shader.shader")
        if not example_file.exists():
            print(f"❌ 示例文件不存在: {example_file}")
            return False
        
        # 执行分析
        result = analyzer.analyze_shader_file(str(example_file))
        
        print(f"✅ 分析完成: {result.shader_name}")
        print(f"   - 复杂度评分: {result.complexity_score:.1f}")
        print(f"   - 函数数量: {len(result.functions)}")
        print(f"   - 属性数量: {len(result.properties)}")
        
        # 保存测试结果
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        analyzer.save_analysis_results([result], str(output_dir))
        print(f"✅ 结果已保存到: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_shader():
    """创建测试用的Shader文件"""
    print("\n📝 创建测试Shader文件...")
    
    shader_content = '''Shader "Custom/TestShader" {
    Properties {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
        _Metallic ("Metallic", Range(0,1)) = 0.0
        _Smoothness ("Smoothness", Range(0,1)) = 0.5
    }
    
    SubShader {
        Tags { "RenderType"="Opaque" }
        LOD 200
        
        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0
        
        sampler2D _MainTex;
        fixed4 _Color;
        half _Metallic;
        half _Smoothness;
        
        struct Input {
            float2 uv_MainTex;
        };
        
        void surf (Input IN, inout SurfaceOutputStandard o) {
            fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = c.rgb;
            o.Metallic = _Metallic;
            o.Smoothness = _Smoothness;
            o.Alpha = c.a;
        }
        ENDCG
    }
    
    FallBack "Diffuse"
}'''
    
    # 确保目录存在
    example_dir = Path("example_shaders")
    example_dir.mkdir(exist_ok=True)
    
    # 写入文件
    shader_file = example_dir / "basic_shader.shader"
    with open(shader_file, 'w', encoding='utf-8') as f:
        f.write(shader_content)
    
    print(f"✅ 测试Shader文件已创建: {shader_file}")

def main():
    """主测试函数"""
    print("🚀 Unity Shader分析器 - 基础功能测试")
    print("=" * 60)
    
    # 创建测试文件
    create_test_shader()
    
    # 运行测试
    tests = [
        ("Shader解析器", test_shader_parser),
        ("模型管理器", test_model_manager),
        ("下载脚本", test_download_script),
        ("基础分析", test_basic_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统基础功能正常")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
    
    print("\n💡 下一步:")
    print("  1. 运行 setup_env.bat 设置完整环境")
    print("  2. 下载模型: python download_model.py --model 0.5B")
    print("  3. 运行完整分析: python shader_analyzer.py --help")

if __name__ == "__main__":
    main()
