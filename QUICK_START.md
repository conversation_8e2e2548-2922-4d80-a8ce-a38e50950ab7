# Unity Shader原子化拆解工具 - 快速开始

## 🚀 项目已完成设置

### 项目结构
```
ShaderFormatter/
├── 📄 README.md              # 完整使用文档
├── 📄 QUICK_START.md         # 本快速指南
├── 🔧 shader_analyzer.py     # 主要分析工具
├── 📥 download_model.py      # 模型下载脚本
├── 📋 requirements.txt       # 依赖列表
├── ⚙️ check_installation.py  # 安装检查
├── ▶️ run_analysis.bat       # Windows批处理启动器
├── 📁 example_shaders/       # 示例shader文件
│   └── basic_shader.shader   # 基础示例
├── 📁 models/                # 模型存储目录（下载后）
└── 📁 venv/                  # 虚拟环境
```

## 🔧 安装步骤

### 1. 检查环境
```bash
python check_installation.py
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 下载模型（约4-5GB）
```bash
python download_model.py
```

### 4. 运行分析
```bash
# 分析单个文件
python shader_analyzer.py -i example_shaders/basic_shader.shader

# 分析整个目录
python shader_analyzer.py -i example_shaders -o ./output

# Windows用户可直接双击
run_analysis.bat
```

## 📊 功能特点

### ✅ 已实现功能
- **基础分析**: 提取shader属性、函数、Pass结构
- **原子化拆解**: 自动识别可重用代码片段
- **文件处理**: 支持单个文件和批量目录处理
- **结果输出**: JSON格式分析结果
- **组件库**: 生成可重用原子组件文件

### 🎯 使用示例

#### 分析单个Shader
```bash
python shader_analyzer.py -i example_shaders/basic_shader.shader -o ./results
```

#### 分析Unity项目
```bash
python shader_analyzer.py -i "C:/UnityProject/Assets/Shaders" -o ./unity_analysis
```

## 📋 输出说明

### 生成的文件
- `*_analysis.json`: 单个shader的详细分析
- `shader_analysis_summary.json`: 所有shader的汇总分析
- `atomic_components/`: 可重用代码片段库
- `component_index.json`: 组件索引文件

### 分析内容
- **属性分析**: Shader属性列表及类型
- **函数拆解**: 函数签名和复杂度评估
- **Pass识别**: 渲染Pass结构
- **原子组件**: 可重用的代码片段
- **优化建议**: 基于模式的优化提示

## 🔍 故障排除

### 常见问题

1. **Python版本问题**
   ```bash
   python --version  # 确保Python 3.8+
   ```

2. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install torch transformers huggingface-hub
   ```

3. **模型下载失败**
   - 检查网络连接
   - 查看 `MANUAL_DOWNLOAD.md` 获取手动下载指南
   - 使用代理或VPN访问Hugging Face

4. **运行权限问题**
   - Windows: 以管理员身份运行CMD
   - 确保Python已添加到PATH

## 🚀 下一步操作

1. **立即开始**: 运行 `python check_installation.py` 检查环境
2. **下载模型**: 运行 `python download_model.py` 获取Gemma2b模型
3. **测试运行**: 运行示例 `python shader_analyzer.py -i example_shaders/basic_shader.shader`
4. **批量处理**: 将你的shader目录复制到项目中进行分析

## 📞 技术支持

- **项目路径**: `e:\PythonDev\BaiduSyncdisk\PythonTools\Python\ShaderFormatter`
- **依赖环境**: Python 3.8+, PyTorch, Transformers
- **模型大小**: 约4-5GB (Gemma-2b-it)
- **存储需求**: 至少10GB可用空间

## 🎉 恭喜！项目已就绪

你的Unity Shader原子化拆解工具已经完成设置，现在可以：

1. **分析现有shader**: 使用提供的示例或你自己的shader文件
2. **生成组件库**: 自动创建可重用的原子组件
3. **优化shader**: 基于分析结果进行优化
4. **批量处理**: 分析整个Unity项目的shader文件

开始你的shader分析之旅吧！