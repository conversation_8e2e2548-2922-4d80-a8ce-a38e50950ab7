#!/usr/bin/env python3
"""
腾讯混元模型管理器
负责不同规模模型的加载、切换和内存管理
"""

import os
import json
import gc
import psutil
import torch
from typing import Dict, Optional, Any, Tuple
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HunyuanModelManager:
    """腾讯混元模型管理器"""
    
    def __init__(self, models_dir: str = "./models"):
        self.models_dir = models_dir
        self.config_file = os.path.join(models_dir, "model_config.json")
        self.current_model = None
        self.current_tokenizer = None
        self.current_model_size = None
        self.model_configs = self._load_model_configs()
        
    def _load_model_configs(self) -> Dict[str, Any]:
        """加载模型配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
                return {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用的模型列表"""
        available = {}
        for size, config in self.model_configs.items():
            local_dir = config.get("local_dir", "")
            if os.path.exists(local_dir):
                # 检查模型文件是否完整
                if self._is_model_complete(local_dir):
                    available[size] = config
                else:
                    logger.warning(f"模型 {size} 文件不完整: {local_dir}")
        return available
    
    def _is_model_complete(self, model_dir: str) -> bool:
        """检查模型文件是否完整"""
        required_files = ["config.json", "tokenizer.json", "tokenizer_config.json"]
        model_files = ["pytorch_model.bin", "model.safetensors"]
        
        # 检查必需文件
        for file in required_files:
            if not os.path.exists(os.path.join(model_dir, file)):
                return False
        
        # 检查模型权重文件（至少有一个）
        has_model_file = any(
            os.path.exists(os.path.join(model_dir, file)) 
            for file in model_files
        )
        
        return has_model_file
    
    def get_memory_info(self) -> Dict[str, float]:
        """获取系统内存信息"""
        memory = psutil.virtual_memory()
        gpu_memory = {}
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                gpu_memory[f"GPU_{i}"] = {
                    "total": torch.cuda.get_device_properties(i).total_memory / 1024**3,
                    "allocated": torch.cuda.memory_allocated(i) / 1024**3,
                    "cached": torch.cuda.memory_reserved(i) / 1024**3
                }
        
        return {
            "system_total": memory.total / 1024**3,
            "system_available": memory.available / 1024**3,
            "system_used": memory.used / 1024**3,
            "gpu_info": gpu_memory
        }
    
    def can_load_model(self, model_size: str) -> Tuple[bool, str]:
        """检查是否可以加载指定规模的模型"""
        if model_size not in self.model_configs:
            return False, f"不支持的模型规模: {model_size}"
        
        config = self.model_configs[model_size]
        local_dir = config.get("local_dir", "")
        
        if not os.path.exists(local_dir):
            return False, f"模型未下载: {model_size}"
        
        if not self._is_model_complete(local_dir):
            return False, f"模型文件不完整: {model_size}"
        
        # 检查内存需求
        memory_info = self.get_memory_info()
        required_memory = self._parse_memory_requirement(config.get("memory_requirement", "0GB"))
        
        if memory_info["system_available"] < required_memory:
            return False, f"内存不足，需要 {required_memory:.1f}GB，可用 {memory_info['system_available']:.1f}GB"
        
        return True, "可以加载"
    
    def _parse_memory_requirement(self, memory_str: str) -> float:
        """解析内存需求字符串"""
        try:
            if "GB" in memory_str:
                return float(memory_str.replace("GB", ""))
            elif "MB" in memory_str:
                return float(memory_str.replace("MB", "")) / 1024
            else:
                return 0.0
        except:
            return 0.0
    
    def unload_current_model(self):
        """卸载当前模型以释放内存"""
        if self.current_model is not None:
            logger.info(f"卸载当前模型: {self.current_model_size}")
            del self.current_model
            del self.current_tokenizer
            self.current_model = None
            self.current_tokenizer = None
            self.current_model_size = None
            
            # 强制垃圾回收
            gc.collect()
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            logger.info("模型卸载完成")
    
    def load_model(self, model_size: str, device: str = "auto", 
                   torch_dtype: str = "auto", force_reload: bool = False) -> bool:
        """加载指定规模的模型"""
        try:
            # 检查是否可以加载
            can_load, message = self.can_load_model(model_size)
            if not can_load:
                logger.error(f"无法加载模型: {message}")
                return False
            
            # 如果已经加载了相同模型且不强制重载，直接返回
            if (self.current_model_size == model_size and 
                self.current_model is not None and not force_reload):
                logger.info(f"模型 {model_size} 已加载")
                return True
            
            # 卸载当前模型
            if self.current_model is not None:
                self.unload_current_model()
            
            config = self.model_configs[model_size]
            local_dir = config["local_dir"]
            
            logger.info(f"开始加载模型: {model_size}")
            logger.info(f"模型路径: {local_dir}")
            
            # 设置torch数据类型
            if torch_dtype == "auto":
                dtype = torch.bfloat16 if torch.cuda.is_available() else torch.float32
            else:
                dtype = getattr(torch, torch_dtype)
            
            # 加载tokenizer
            logger.info("加载tokenizer...")
            self.current_tokenizer = AutoTokenizer.from_pretrained(
                local_dir,
                trust_remote_code=True,
                use_fast=True
            )
            
            # 加载模型
            logger.info("加载模型...")
            self.current_model = AutoModelForCausalLM.from_pretrained(
                local_dir,
                device_map=device,
                torch_dtype=dtype,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            self.current_model_size = model_size
            
            # 显示内存使用情况
            memory_info = self.get_memory_info()
            logger.info(f"模型 {model_size} 加载完成")
            logger.info(f"系统内存使用: {memory_info['system_used']:.1f}GB / {memory_info['system_total']:.1f}GB")
            
            if torch.cuda.is_available():
                for gpu_id, gpu_info in memory_info["gpu_info"].items():
                    logger.info(f"{gpu_id} 显存使用: {gpu_info['allocated']:.1f}GB / {gpu_info['total']:.1f}GB")
            
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            self.unload_current_model()
            return False
    
    def get_current_model_info(self) -> Optional[Dict[str, Any]]:
        """获取当前加载的模型信息"""
        if self.current_model_size is None:
            return None
        
        config = self.model_configs.get(self.current_model_size, {})
        memory_info = self.get_memory_info()
        
        return {
            "size": self.current_model_size,
            "name": config.get("name", ""),
            "description": config.get("description", ""),
            "local_dir": config.get("local_dir", ""),
            "memory_requirement": config.get("memory_requirement", ""),
            "current_memory_usage": memory_info
        }
    
    def generate_text(self, prompt: str, max_length: int = 512, 
                     temperature: float = 0.7, top_p: float = 0.8,
                     do_sample: bool = True) -> Optional[str]:
        """使用当前模型生成文本"""
        if self.current_model is None or self.current_tokenizer is None:
            logger.error("没有加载模型")
            return None
        
        try:
            # 编码输入
            inputs = self.current_tokenizer.encode(prompt, return_tensors="pt")
            
            # 移动到模型设备
            if hasattr(self.current_model, 'device'):
                inputs = inputs.to(self.current_model.device)
            
            # 生成文本
            with torch.no_grad():
                outputs = self.current_model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=do_sample,
                    pad_token_id=self.current_tokenizer.eos_token_id
                )
            
            # 解码输出
            generated_text = self.current_tokenizer.decode(
                outputs[0], 
                skip_special_tokens=True
            )
            
            # 移除输入部分
            if generated_text.startswith(prompt):
                generated_text = generated_text[len(prompt):].strip()
            
            return generated_text
            
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            return None
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self.unload_current_model()
