@echo off
echo 🚀 正在设置Unity Shader分析器环境...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

REM 删除旧的虚拟环境
if exist venv (
    echo 🗑️ 删除旧的虚拟环境...
    rmdir /s /q venv
)

echo 📦 创建新的虚拟环境...
python -m venv venv
if errorlevel 1 (
    echo ❌ 错误: 创建虚拟环境失败
    pause
    exit /b 1
)

echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

echo 📈 升级pip...
python -m pip install --upgrade pip setuptools wheel

echo 📚 安装基础依赖...
pip install huggingface-hub transformers torch --index-url https://download.pytorch.org/whl/cpu

echo 📋 安装项目依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ⚠️ 警告: 部分依赖安装失败，但核心功能应该可用
)

echo 🤖 检查模型下载器...
python download_model.py --list
if errorlevel 1 (
    echo ⚠️ 警告: 模型下载器测试失败
)

echo.
echo ✅ 环境设置完成！
echo.
echo 📖 使用方法:
echo   1. 激活环境: venv\Scripts\activate
echo   2. 下载模型: python download_model.py --model 1.8B
echo   3. 运行分析: python shader_analyzer.py --help
echo   4. 查看指南: type MODEL_USAGE_GUIDE.md
echo.
echo 💡 建议先下载较小的模型进行测试:
echo   python download_model.py --model 0.5B
echo.
pause