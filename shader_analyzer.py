#!/usr/bin/env python3
"""
Unity Shader原子化分析器
使用腾讯混元模型进行智能Shader代码分析和原子化拆解
"""

import os
import sys
import json
import argparse
import re
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

# 导入模型管理器
from model_manager import HunyuanModelManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ShaderProperty:
    """Shader属性"""
    name: str
    type: str
    display_name: str = ""
    default_value: str = ""
    description: str = ""

@dataclass
class ShaderFunction:
    """Shader函数"""
    name: str
    return_type: str
    parameters: List[str]
    body: str
    description: str = ""
    complexity: str = "未知"
    atomic_components: List[str] = None

@dataclass
class ShaderPass:
    """Shader Pass"""
    name: str
    tags: Dict[str, str]
    vertex_shader: str = ""
    fragment_shader: str = ""
    geometry_shader: str = ""
    hull_shader: str = ""
    domain_shader: str = ""
    compute_shader: str = ""
    description: str = ""

@dataclass
class AtomicComponent:
    """原子组件"""
    name: str
    type: str
    code: str
    description: str = ""
    reusable: bool = True
    dependencies: List[str] = None
    usage_count: int = 0

@dataclass
class ShaderAnalysisResult:
    """Shader分析结果"""
    shader_name: str
    file_path: str
    properties: List[ShaderProperty]
    functions: List[ShaderFunction]
    passes: List[ShaderPass]
    atomic_components: List[AtomicComponent]
    global_variables: List[str]
    includes: List[str]
    optimization_suggestions: List[str]
    complexity_score: float
    analysis_timestamp: str

class ShaderParser:
    """Shader解析器"""

    def __init__(self):
        self.shader_content = ""
        self.lines = []

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """解析Shader文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.shader_content = f.read()

            self.lines = self.shader_content.split('\n')

            result = {
                "shader_name": self._extract_shader_name(),
                "properties": self._extract_properties(),
                "subshaders": self._extract_subshaders(),
                "passes": self._extract_passes(),
                "functions": self._extract_functions(),
                "global_variables": self._extract_global_variables(),
                "includes": self._extract_includes(),
                "raw_content": self.shader_content
            }

            return result

        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            return {}

    def _extract_shader_name(self) -> str:
        """提取Shader名称"""
        pattern = r'Shader\s+"([^"]+)"'
        match = re.search(pattern, self.shader_content)
        return match.group(1) if match else "Unknown"

    def _extract_properties(self) -> List[Dict[str, str]]:
        """提取Properties"""
        properties = []
        in_properties = False

        for line in self.lines:
            line = line.strip()

            if line.startswith("Properties"):
                in_properties = True
                continue
            elif in_properties and line == "}":
                break
            elif in_properties and line:
                prop = self._parse_property_line(line)
                if prop:
                    properties.append(prop)

        return properties

    def _parse_property_line(self, line: str) -> Optional[Dict[str, str]]:
        """解析单个属性行"""
        # 匹配格式: _PropertyName ("Display Name", Type) = DefaultValue
        pattern = r'(\w+)\s*\(\s*"([^"]*)",\s*(\w+)\s*\)\s*=\s*(.+)'
        match = re.search(pattern, line)

        if match:
            return {
                "name": match.group(1),
                "display_name": match.group(2),
                "type": match.group(3),
                "default_value": match.group(4).strip()
            }
        return None

    def _extract_subshaders(self) -> List[Dict[str, Any]]:
        """提取SubShader"""
        subshaders = []
        # 这里可以添加SubShader解析逻辑
        return subshaders

    def _extract_passes(self) -> List[Dict[str, Any]]:
        """提取Pass"""
        passes = []
        pass_pattern = r'Pass\s*{([^}]+)}'

        for match in re.finditer(pass_pattern, self.shader_content, re.DOTALL):
            pass_content = match.group(1)
            pass_info = {
                "content": pass_content,
                "tags": self._extract_pass_tags(pass_content),
                "shaders": self._extract_pass_shaders(pass_content)
            }
            passes.append(pass_info)

        return passes

    def _extract_pass_tags(self, pass_content: str) -> Dict[str, str]:
        """提取Pass标签"""
        tags = {}
        tag_pattern = r'Tags\s*{\s*([^}]+)\s*}'
        match = re.search(tag_pattern, pass_content)

        if match:
            tag_content = match.group(1)
            # 解析标签键值对
            tag_pairs = re.findall(r'"([^"]+)"\s*=\s*"([^"]+)"', tag_content)
            tags = dict(tag_pairs)

        return tags

    def _extract_pass_shaders(self, pass_content: str) -> Dict[str, str]:
        """提取Pass中的着色器"""
        shaders = {}

        # 查找CGPROGRAM/ENDCG块
        cg_pattern = r'CGPROGRAM(.*?)ENDCG'
        cg_match = re.search(cg_pattern, pass_content, re.DOTALL)

        if cg_match:
            cg_content = cg_match.group(1)
            shaders["cg_content"] = cg_content

            # 提取pragma指令
            pragma_pattern = r'#pragma\s+(\w+)\s+(\w+)'
            pragmas = re.findall(pragma_pattern, cg_content)
            shaders["pragmas"] = pragmas

        return shaders

    def _extract_functions(self) -> List[Dict[str, Any]]:
        """提取函数"""
        functions = []

        # 匹配函数定义
        function_pattern = r'(\w+)\s+(\w+)\s*\([^)]*\)\s*{[^}]*}'

        for match in re.finditer(function_pattern, self.shader_content, re.DOTALL):
            func_text = match.group(0)
            return_type = match.group(1)
            func_name = match.group(2)

            # 跳过一些关键字
            if return_type.lower() in ['shader', 'subshader', 'pass', 'properties']:
                continue

            functions.append({
                "name": func_name,
                "return_type": return_type,
                "full_text": func_text,
                "parameters": self._extract_function_parameters(func_text)
            })

        return functions

    def _extract_function_parameters(self, func_text: str) -> List[str]:
        """提取函数参数"""
        param_pattern = r'\(([^)]*)\)'
        match = re.search(param_pattern, func_text)

        if match:
            params_str = match.group(1).strip()
            if params_str:
                return [p.strip() for p in params_str.split(',')]

        return []

    def _extract_global_variables(self) -> List[str]:
        """提取全局变量"""
        variables = []

        # 匹配变量声明
        var_patterns = [
            r'sampler2D\s+(\w+)',
            r'float\d*\s+(\w+)',
            r'int\d*\s+(\w+)',
            r'bool\s+(\w+)',
            r'fixed\d*\s+(\w+)'
        ]

        for pattern in var_patterns:
            matches = re.findall(pattern, self.shader_content)
            variables.extend(matches)

        return list(set(variables))

    def _extract_includes(self) -> List[str]:
        """提取包含文件"""
        includes = []
        include_pattern = r'#include\s+"([^"]+)"'
        matches = re.findall(include_pattern, self.shader_content)
        return matches

class ShaderAIAnalyzer:
    """基于腾讯混元模型的Shader AI分析器"""

    def __init__(self, model_manager: HunyuanModelManager):
        self.model_manager = model_manager
        self.analysis_prompts = self._load_analysis_prompts()

    def _load_analysis_prompts(self) -> Dict[str, str]:
        """加载分析提示词"""
        return {
            "function_analysis": """
请分析以下Unity Shader函数，提供详细的功能描述、复杂度评估和原子化建议：

函数代码：
{function_code}

请按以下格式回答：
1. 功能描述：[简要描述函数的主要功能]
2. 复杂度：[低/中/高]
3. 原子组件：[列出可以拆分的原子组件]
4. 优化建议：[提供优化建议]
5. 依赖分析：[分析函数依赖的变量和函数]
""",

            "component_extraction": """
请从以下Shader代码中提取可重用的原子组件：

代码：
{shader_code}

请识别并提取：
1. 可重用的计算片段
2. 通用的变换操作
3. 标准的光照计算
4. 纹理采样操作
5. 数学运算函数

对每个组件，请提供：
- 组件名称
- 组件类型
- 代码片段
- 使用场景
- 重用价值评估
""",

            "optimization_analysis": """
请分析以下Shader代码的性能优化机会：

代码：
{shader_code}

请从以下角度分析：
1. 性能瓶颈识别
2. 冗余计算检测
3. 内存访问优化
4. 指令级优化
5. 平台特定优化

请提供具体的优化建议和改进后的代码示例。
""",

            "complexity_evaluation": """
请评估以下Shader的复杂度：

代码：
{shader_code}

请从以下维度评分（1-10分）：
1. 算法复杂度
2. 内存使用复杂度
3. 渲染管线复杂度
4. 维护复杂度
5. 性能影响

并给出总体复杂度评分和详细说明。
"""
        }

    def analyze_function(self, function_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个函数"""
        try:
            prompt = self.analysis_prompts["function_analysis"].format(
                function_code=function_info.get("full_text", "")
            )

            response = self.model_manager.generate_text(
                prompt=prompt,
                max_length=1024,
                temperature=0.3
            )

            if response:
                return self._parse_function_analysis(response)
            else:
                return self._get_fallback_function_analysis(function_info)

        except Exception as e:
            logger.error(f"函数分析失败: {e}")
            return self._get_fallback_function_analysis(function_info)

    def extract_atomic_components(self, shader_code: str) -> List[Dict[str, Any]]:
        """提取原子组件"""
        try:
            prompt = self.analysis_prompts["component_extraction"].format(
                shader_code=shader_code
            )

            response = self.model_manager.generate_text(
                prompt=prompt,
                max_length=2048,
                temperature=0.2
            )

            if response:
                return self._parse_component_extraction(response)
            else:
                return self._get_fallback_components(shader_code)

        except Exception as e:
            logger.error(f"组件提取失败: {e}")
            return self._get_fallback_components(shader_code)

    def analyze_optimization_opportunities(self, shader_code: str) -> List[str]:
        """分析优化机会"""
        try:
            prompt = self.analysis_prompts["optimization_analysis"].format(
                shader_code=shader_code
            )

            response = self.model_manager.generate_text(
                prompt=prompt,
                max_length=1536,
                temperature=0.3
            )

            if response:
                return self._parse_optimization_suggestions(response)
            else:
                return self._get_fallback_optimizations(shader_code)

        except Exception as e:
            logger.error(f"优化分析失败: {e}")
            return self._get_fallback_optimizations(shader_code)

    def evaluate_complexity(self, shader_code: str) -> float:
        """评估复杂度"""
        try:
            prompt = self.analysis_prompts["complexity_evaluation"].format(
                shader_code=shader_code
            )

            response = self.model_manager.generate_text(
                prompt=prompt,
                max_length=1024,
                temperature=0.2
            )

            if response:
                return self._parse_complexity_score(response)
            else:
                return self._calculate_fallback_complexity(shader_code)

        except Exception as e:
            logger.error(f"复杂度评估失败: {e}")
            return self._calculate_fallback_complexity(shader_code)

    def _parse_function_analysis(self, response: str) -> Dict[str, Any]:
        """解析函数分析结果"""
        result = {
            "description": "",
            "complexity": "未知",
            "atomic_components": [],
            "optimization_suggestions": [],
            "dependencies": []
        }

        lines = response.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if "功能描述：" in line:
                result["description"] = line.split("功能描述：", 1)[1].strip()
            elif "复杂度：" in line:
                result["complexity"] = line.split("复杂度：", 1)[1].strip()
            elif "原子组件：" in line:
                components = line.split("原子组件：", 1)[1].strip()
                result["atomic_components"] = [c.strip() for c in components.split(',') if c.strip()]
            elif "优化建议：" in line:
                suggestions = line.split("优化建议：", 1)[1].strip()
                result["optimization_suggestions"] = [suggestions] if suggestions else []
            elif "依赖分析：" in line:
                deps = line.split("依赖分析：", 1)[1].strip()
                result["dependencies"] = [d.strip() for d in deps.split(',') if d.strip()]

        return result

    def _parse_component_extraction(self, response: str) -> List[Dict[str, Any]]:
        """解析组件提取结果"""
        components = []
        # 这里可以添加更复杂的解析逻辑
        # 暂时返回基础解析结果
        return components

    def _parse_optimization_suggestions(self, response: str) -> List[str]:
        """解析优化建议"""
        suggestions = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                suggestions.append(line[1:].strip())

        return suggestions

    def _parse_complexity_score(self, response: str) -> float:
        """解析复杂度评分"""
        # 查找数字评分
        import re
        scores = re.findall(r'(\d+(?:\.\d+)?)', response)

        if scores:
            # 取最后一个数字作为总体评分
            try:
                return float(scores[-1])
            except:
                pass

        return 5.0  # 默认中等复杂度

    def _get_fallback_function_analysis(self, function_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取函数分析的后备结果"""
        return {
            "description": f"函数 {function_info.get('name', 'unknown')} 的基础分析",
            "complexity": "中",
            "atomic_components": [],
            "optimization_suggestions": ["建议使用AI模型进行详细分析"],
            "dependencies": []
        }

    def _get_fallback_components(self, shader_code: str) -> List[Dict[str, Any]]:
        """获取组件提取的后备结果"""
        return []

    def _get_fallback_optimizations(self, shader_code: str) -> List[str]:
        """获取优化建议的后备结果"""
        return [
            "建议使用AI模型进行详细优化分析",
            "检查是否有冗余计算",
            "考虑使用更高效的内置函数"
        ]

    def _calculate_fallback_complexity(self, shader_code: str) -> float:
        """计算复杂度的后备方法"""
        # 基于代码行数和关键字数量的简单评估
        lines = len(shader_code.split('\n'))
        keywords = len(re.findall(r'\b(if|for|while|switch|tex2D|mul|dot|cross|normalize)\b', shader_code))

        # 简单的复杂度计算
        complexity = min(10.0, (lines / 10.0) + (keywords / 5.0))
        return max(1.0, complexity)

class UnityShaderAnalyzer:
    """Unity Shader原子化分析器主类"""

    def __init__(self, model_size: str = "1.8B", models_dir: str = "./models"):
        self.model_manager = HunyuanModelManager(models_dir)
        self.parser = ShaderParser()
        self.ai_analyzer = None
        self.model_size = model_size

        # 尝试加载模型
        self._initialize_model()

    def _initialize_model(self):
        """初始化模型"""
        try:
            logger.info(f"正在初始化模型: {self.model_size}")

            if self.model_manager.load_model(self.model_size):
                self.ai_analyzer = ShaderAIAnalyzer(self.model_manager)
                logger.info("模型初始化成功")
            else:
                logger.warning("模型加载失败，将使用基础分析功能")

        except Exception as e:
            logger.error(f"模型初始化失败: {e}")

    def analyze_shader_file(self, file_path: str) -> ShaderAnalysisResult:
        """分析单个Shader文件"""
        logger.info(f"开始分析Shader文件: {file_path}")

        # 解析文件
        parsed_data = self.parser.parse_file(file_path)
        if not parsed_data:
            raise ValueError(f"无法解析文件: {file_path}")

        # 创建分析结果
        result = ShaderAnalysisResult(
            shader_name=parsed_data.get("shader_name", "Unknown"),
            file_path=file_path,
            properties=[],
            functions=[],
            passes=[],
            atomic_components=[],
            global_variables=parsed_data.get("global_variables", []),
            includes=parsed_data.get("includes", []),
            optimization_suggestions=[],
            complexity_score=0.0,
            analysis_timestamp=datetime.now().isoformat()
        )

        # 转换属性
        for prop_data in parsed_data.get("properties", []):
            prop = ShaderProperty(
                name=prop_data.get("name", ""),
                type=prop_data.get("type", ""),
                display_name=prop_data.get("display_name", ""),
                default_value=prop_data.get("default_value", ""),
                description=""
            )
            result.properties.append(prop)

        # 分析函数
        for func_data in parsed_data.get("functions", []):
            func_analysis = {}

            if self.ai_analyzer:
                func_analysis = self.ai_analyzer.analyze_function(func_data)

            func = ShaderFunction(
                name=func_data.get("name", ""),
                return_type=func_data.get("return_type", ""),
                parameters=func_data.get("parameters", []),
                body=func_data.get("full_text", ""),
                description=func_analysis.get("description", ""),
                complexity=func_analysis.get("complexity", "未知"),
                atomic_components=func_analysis.get("atomic_components", [])
            )
            result.functions.append(func)

        # 转换Pass
        for pass_data in parsed_data.get("passes", []):
            pass_obj = ShaderPass(
                name=f"Pass_{len(result.passes)}",
                tags=pass_data.get("tags", {}),
                vertex_shader="",
                fragment_shader="",
                description=""
            )

            # 提取着色器代码
            shaders = pass_data.get("shaders", {})
            if "cg_content" in shaders:
                # 这里可以进一步解析顶点和片段着色器
                pass_obj.vertex_shader = shaders["cg_content"]
                pass_obj.fragment_shader = shaders["cg_content"]

            result.passes.append(pass_obj)

        # AI分析
        if self.ai_analyzer:
            shader_code = parsed_data.get("raw_content", "")

            # 提取原子组件
            atomic_components_data = self.ai_analyzer.extract_atomic_components(shader_code)
            for comp_data in atomic_components_data:
                component = AtomicComponent(
                    name=comp_data.get("name", ""),
                    type=comp_data.get("type", ""),
                    code=comp_data.get("code", ""),
                    description=comp_data.get("description", ""),
                    reusable=comp_data.get("reusable", True),
                    dependencies=comp_data.get("dependencies", [])
                )
                result.atomic_components.append(component)

            # 优化建议
            result.optimization_suggestions = self.ai_analyzer.analyze_optimization_opportunities(shader_code)

            # 复杂度评估
            result.complexity_score = self.ai_analyzer.evaluate_complexity(shader_code)

        logger.info(f"Shader分析完成: {result.shader_name}")
        return result

    def analyze_directory(self, directory_path: str) -> List[ShaderAnalysisResult]:
        """分析目录中的所有Shader文件"""
        results = []
        shader_extensions = ['.shader', '.hlsl', '.cginc']

        directory = Path(directory_path)
        if not directory.exists():
            raise ValueError(f"目录不存在: {directory_path}")

        shader_files = []
        for ext in shader_extensions:
            shader_files.extend(directory.rglob(f"*{ext}"))

        logger.info(f"找到 {len(shader_files)} 个Shader文件")

        for file_path in shader_files:
            try:
                result = self.analyze_shader_file(str(file_path))
                results.append(result)
            except Exception as e:
                logger.error(f"分析文件失败 {file_path}: {e}")

        return results

    def save_analysis_results(self, results: List[ShaderAnalysisResult], output_dir: str):
        """保存分析结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存每个文件的分析结果
        for result in results:
            filename = f"{result.shader_name.replace('/', '_')}_analysis.json"
            file_path = output_path / filename

            # 转换为字典格式
            result_dict = asdict(result)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, ensure_ascii=False, indent=2)

            logger.info(f"分析结果已保存: {file_path}")

        # 保存汇总报告
        self._save_summary_report(results, output_path)

        # 保存原子组件库
        self._save_atomic_components_library(results, output_path)

    def _save_summary_report(self, results: List[ShaderAnalysisResult], output_path: Path):
        """保存汇总报告"""
        summary = {
            "analysis_summary": {
                "total_shaders": len(results),
                "analysis_timestamp": datetime.now().isoformat(),
                "model_used": self.model_size,
                "average_complexity": sum(r.complexity_score for r in results) / len(results) if results else 0
            },
            "shaders": []
        }

        for result in results:
            shader_summary = {
                "name": result.shader_name,
                "file_path": result.file_path,
                "complexity_score": result.complexity_score,
                "function_count": len(result.functions),
                "pass_count": len(result.passes),
                "atomic_component_count": len(result.atomic_components),
                "optimization_suggestion_count": len(result.optimization_suggestions)
            }
            summary["shaders"].append(shader_summary)

        summary_file = output_path / "analysis_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"汇总报告已保存: {summary_file}")

    def _save_atomic_components_library(self, results: List[ShaderAnalysisResult], output_path: Path):
        """保存原子组件库"""
        components_dir = output_path / "atomic_components"
        components_dir.mkdir(exist_ok=True)

        all_components = []
        component_index = {}

        for result in results:
            for component in result.atomic_components:
                component_dict = asdict(component)
                component_dict["source_shader"] = result.shader_name
                all_components.append(component_dict)

                # 建立索引
                comp_type = component.type
                if comp_type not in component_index:
                    component_index[comp_type] = []
                component_index[comp_type].append(component.name)

        # 保存所有组件
        components_file = components_dir / "all_components.json"
        with open(components_file, 'w', encoding='utf-8') as f:
            json.dump(all_components, f, ensure_ascii=False, indent=2)

        # 保存组件索引
        index_file = components_dir / "component_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(component_index, f, ensure_ascii=False, indent=2)

        logger.info(f"原子组件库已保存: {components_dir}")

    def get_model_info(self) -> Optional[Dict[str, Any]]:
        """获取当前模型信息"""
        return self.model_manager.get_current_model_info()

    def switch_model(self, model_size: str) -> bool:
        """切换模型"""
        try:
            if self.model_manager.load_model(model_size):
                self.model_size = model_size
                self.ai_analyzer = ShaderAIAnalyzer(self.model_manager)
                logger.info(f"已切换到模型: {model_size}")
                return True
            else:
                logger.error(f"切换模型失败: {model_size}")
                return False
        except Exception as e:
            logger.error(f"切换模型异常: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Unity Shader原子化分析器 - 使用腾讯混元模型进行智能分析",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析单个Shader文件
  python shader_analyzer.py -i shader.hlsl -o ./output

  # 分析整个目录
  python shader_analyzer.py -i ./shaders -o ./output

  # 使用特定模型
  python shader_analyzer.py -i shader.hlsl -m 4B -o ./output

  # 显示可用模型
  python shader_analyzer.py --list-models

  # 显示模型信息
  python shader_analyzer.py --model-info
        """
    )

    parser.add_argument(
        "-i", "--input",
        type=str,
        help="输入的Shader文件或目录路径"
    )

    parser.add_argument(
        "-o", "--output",
        type=str,
        default="./output",
        help="输出目录 (默认: ./output)"
    )

    parser.add_argument(
        "-m", "--model",
        type=str,
        choices=["0.5B", "1.8B", "4B", "7B"],
        default="1.8B",
        help="使用的模型规模 (默认: 1.8B)"
    )

    parser.add_argument(
        "--models-dir",
        type=str,
        default="./models",
        help="模型存储目录 (默认: ./models)"
    )

    parser.add_argument(
        "--list-models",
        action="store_true",
        help="列出可用的模型"
    )

    parser.add_argument(
        "--model-info",
        action="store_true",
        help="显示当前模型信息"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 创建分析器
        analyzer = UnityShaderAnalyzer(
            model_size=args.model,
            models_dir=args.models_dir
        )

        # 处理特殊命令
        if args.list_models:
            available_models = analyzer.model_manager.get_available_models()
            print("\n📋 可用的腾讯混元模型:")
            print("=" * 60)

            if not available_models:
                print("❌ 没有找到已下载的模型")
                print("💡 请运行以下命令下载模型:")
                print("   python download_model.py --all")
            else:
                for size, config in available_models.items():
                    print(f"✅ {size:>4} | {config.get('memory_requirement', 'N/A'):>4} | {config.get('description', '')}")

            print("=" * 60)
            return

        if args.model_info:
            model_info = analyzer.get_model_info()
            if model_info:
                print("\n🤖 当前模型信息:")
                print("=" * 50)
                print(f"模型规模: {model_info['size']}")
                print(f"模型名称: {model_info['name']}")
                print(f"描述: {model_info['description']}")
                print(f"本地路径: {model_info['local_dir']}")
                print(f"内存需求: {model_info['memory_requirement']}")

                memory_usage = model_info['current_memory_usage']
                print(f"\n💾 内存使用情况:")
                print(f"系统内存: {memory_usage['system_used']:.1f}GB / {memory_usage['system_total']:.1f}GB")

                if memory_usage['gpu_info']:
                    for gpu_id, gpu_info in memory_usage['gpu_info'].items():
                        print(f"{gpu_id}: {gpu_info['allocated']:.1f}GB / {gpu_info['total']:.1f}GB")
            else:
                print("❌ 没有加载模型")
            return

        # 检查输入参数
        if not args.input:
            print("❌ 请指定输入文件或目录")
            parser.print_help()
            return

        # 检查输入路径
        input_path = Path(args.input)
        if not input_path.exists():
            print(f"❌ 输入路径不存在: {args.input}")
            return

        # 执行分析
        print(f"\n🚀 开始分析...")
        print(f"📁 输入: {args.input}")
        print(f"📁 输出: {args.output}")
        print(f"🤖 模型: {args.model}")

        results = []

        if input_path.is_file():
            # 分析单个文件
            result = analyzer.analyze_shader_file(str(input_path))
            results.append(result)
        else:
            # 分析目录
            results = analyzer.analyze_directory(str(input_path))

        if not results:
            print("❌ 没有找到可分析的Shader文件")
            return

        # 保存结果
        analyzer.save_analysis_results(results, args.output)

        # 显示汇总信息
        print(f"\n✅ 分析完成!")
        print(f"📊 分析统计:")
        print(f"  - 处理文件数: {len(results)}")
        print(f"  - 总函数数: {sum(len(r.functions) for r in results)}")
        print(f"  - 总Pass数: {sum(len(r.passes) for r in results)}")
        print(f"  - 原子组件数: {sum(len(r.atomic_components) for r in results)}")
        print(f"  - 平均复杂度: {sum(r.complexity_score for r in results) / len(results):.1f}")
        print(f"📁 结果保存在: {args.output}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()