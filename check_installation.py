#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装状态检查脚本
"""
import os
import sys
from pathlib import Path

def check_python():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    print(f"Python版本: {sys.version}")
    return True

def check_dependencies():
    """检查依赖库"""
    print("\n📦 检查依赖库...")
    required_packages = [
        'torch', 'transformers', 'huggingface_hub', 'numpy'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n缺失的包: {', '.join(missing)}")
        print("运行: pip install -r requirements.txt")
        return False
    return True

def check_model():
    """检查模型文件"""
    print("\n🤖 检查模型文件...")
    model_path = Path("./models/gemma-2b-it")
    
    if model_path.exists():
        files = list(model_path.rglob("*"))
        if files:
            print(f"✅ 模型文件已就绪 ({len(files)} 个文件)")
            return True
        else:
            print("❌ 模型目录为空")
    else:
        print("❌ 模型目录不存在")
    
    print("运行: python download_model.py")
    return False

def check_examples():
    """检查示例文件"""
    print("\n📄 检查示例文件...")
    example_path = Path("./example_shaders/basic_shader.shader")
    
    if example_path.exists():
        print("✅ 示例shader文件已就绪")
        return True
    else:
        print("❌ 示例shader文件缺失")
        return False

def main():
    """主函数"""
    print("Unity Shader原子化拆解工具 - 安装状态检查")
    print("=" * 50)
    
    checks = [
        ("Python环境", check_python),
        ("依赖库", check_dependencies),
        ("模型文件", check_model),
        ("示例文件", check_examples)
    ]
    
    all_passed = True
    for name, check_func in checks:
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！可以开始使用工具了")
        print("\n运行示例:")
        print("python shader_analyzer.py -i example_shaders/basic_shader.shader")
    else:
        print("⚠️  部分检查未通过，请按提示修复")

if __name__ == "__main__":
    main()