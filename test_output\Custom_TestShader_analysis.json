{"shader_name": "Custom/TestShader", "file_path": "example_shaders\\basic_shader.shader", "properties": [{"name": "_MainTex", "type": "2D", "display_name": "Texture", "default_value": "\"white\" {}", "description": ""}, {"name": "_Color", "type": "Color", "display_name": "Color", "default_value": "(1,1,1,1)", "description": ""}], "functions": [{"name": "surf", "return_type": "void", "parameters": ["Input IN", "inout SurfaceOutputStandard o"], "body": "void surf (Input IN, inout SurfaceOutputStandard o) {\n            fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;\n            o.Albedo = c.rgb;\n            o.Metallic = _Metallic;\n            o.Smoothness = _Smoothness;\n            o.Alpha = c.a;\n        }", "description": "", "complexity": "未知", "atomic_components": []}], "passes": [], "atomic_components": [], "global_variables": ["_Color", "_MainTex", "uv_MainTex", "c"], "includes": [], "optimization_suggestions": [], "complexity_score": 0.0, "analysis_timestamp": "2025-08-04T15:49:54.765652"}