#!/usr/bin/env python3
"""
Unity Shader分析器 - 模型下载脚本
使用开源替代模型进行Shader分析
"""

import os
import sys
import subprocess
import shutil

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def download_alternative_model():
    """下载开源替代模型"""
    try:
        from huggingface_hub import snapshot_download
        
        # 使用开源的CodeBERT模型作为替代
        model_name = "microsoft/codebert-base"
        local_dir = "./models/codebert-base"
        
        print(f"正在下载开源替代模型: {model_name}")
        print(f"目标目录: {local_dir}")
        
        # 创建模型目录
        os.makedirs(local_dir, exist_ok=True)
        
        # 下载模型
        snapshot_download(
            repo_id=model_name,
            local_dir=local_dir,
            local_dir_use_symlinks=False
        )
        
        print(f"✅ 模型下载完成: {local_dir}")
        return True
        
    except ImportError:
        print("安装依赖...")
        if install_package("huggingface_hub"):
            return download_alternative_model()
        else:
            print("❌ 无法安装huggingface_hub")
            return False
    except Exception as e:
        print(f"⚠️ 下载失败: {e}")
        return False

def create_manual_download_guide():
    """创建手动下载指南"""
    guide_content = """# 手动下载模型指南

由于Hugging Face访问限制，我们提供以下替代方案：

## 方案1: 使用开源替代模型

我们已配置使用开源的CodeBERT模型，无需授权即可使用：
- 模型: microsoft/codebert-base
- 自动下载到: ./models/codebert-base/

## 方案2: 使用本地分析（推荐）

ShaderFormatter已内置基础分析功能，无需大模型即可完成：
- 自动识别Shader结构
- 提取Pass、Properties、SubShader等组件
- 生成可重用的原子组件

## 方案3: 使用其他开源模型

如需更强的分析能力，可尝试以下开源模型：
1. Salesforce/codet5-base
2. microsoft/unixcoder-base
3. EleutherAI/gpt-neo-125M

## 使用方法

运行以下命令开始使用：
```bash
python shader_analyzer.py --input example_shaders/basic_shader.shader --output ./output
```

## 验证安装

运行检查脚本验证安装状态：
```bash
python check_installation.py
```
"""
    
    with open("MANUAL_DOWNLOAD.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("📋 已创建手动下载指南: MANUAL_DOWNLOAD.md")

if __name__ == "__main__":
    print("🚀 Unity Shader分析器 - 模型下载")
    print("=" * 50)
    
    # 创建模型目录
    os.makedirs("./models", exist_ok=True)
    
    # 尝试下载开源替代模型
    success = download_alternative_model()
    
    if not success:
        create_manual_download_guide()
        
    print("\n✅ 准备就绪！现在可以运行分析器了")
    print("运行: python shader_analyzer.py --help 查看使用说明")