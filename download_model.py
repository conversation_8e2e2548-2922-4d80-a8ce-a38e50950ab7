#!/usr/bin/env python3
"""
Unity Shader分析器 - 腾讯混元模型下载脚本
支持下载腾讯混元0.5B、1.8B、4B、7B四个不同参数规模的模型
用于Unity Shader代码的原子化拆解分析
"""

import os
import sys
import subprocess
import json
import argparse
from typing import Dict, List, Optional

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

# 腾讯混元模型配置
HUNYUAN_MODELS = {
    "0.5B": {
        "name": "tencent/Hunyuan-0.5B-Instruct",
        "size": "0.5B",
        "description": "最小模型，适合资源受限环境",
        "local_dir": "./models/hunyuan-0.5B-instruct",
        "memory_requirement": "2GB"
    },
    "1.8B": {
        "name": "tencent/Hunyuan-1.8B-Instruct",
        "size": "1.8B",
        "description": "轻量级模型，平衡性能和资源消耗",
        "local_dir": "./models/hunyuan-1.8B-instruct",
        "memory_requirement": "4GB"
    },
    "4B": {
        "name": "tencent/Hunyuan-4B-Instruct",
        "size": "4B",
        "description": "中等规模模型，提供更好的分析能力",
        "local_dir": "./models/hunyuan-4B-instruct",
        "memory_requirement": "8GB"
    },
    "7B": {
        "name": "tencent/Hunyuan-7B-Instruct",
        "size": "7B",
        "description": "大规模模型，最佳分析效果",
        "local_dir": "./models/hunyuan-7B-instruct",
        "memory_requirement": "16GB"
    }
}

def download_hunyuan_model(model_size: str, force_download: bool = False) -> bool:
    """下载指定规模的腾讯混元模型"""
    try:
        from huggingface_hub import snapshot_download

        if model_size not in HUNYUAN_MODELS:
            print(f"❌ 不支持的模型规模: {model_size}")
            print(f"支持的规模: {', '.join(HUNYUAN_MODELS.keys())}")
            return False

        model_config = HUNYUAN_MODELS[model_size]
        model_name = model_config["name"]
        local_dir = model_config["local_dir"]

        # 检查模型是否已存在
        if os.path.exists(local_dir) and not force_download:
            print(f"✅ 模型 {model_size} 已存在: {local_dir}")
            return True

        print(f"🚀 正在下载腾讯混元模型: {model_name}")
        print(f"📁 目标目录: {local_dir}")
        print(f"💾 内存需求: {model_config['memory_requirement']}")
        print(f"📝 描述: {model_config['description']}")

        # 创建模型目录
        os.makedirs(local_dir, exist_ok=True)

        # 下载模型
        snapshot_download(
            repo_id=model_name,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True
        )

        print(f"✅ 模型 {model_size} 下载完成: {local_dir}")
        return True

    except ImportError:
        print("📦 安装依赖...")
        if install_package("huggingface_hub"):
            return download_hunyuan_model(model_size, force_download)
        else:
            print("❌ 无法安装huggingface_hub")
            return False
    except Exception as e:
        print(f"⚠️ 下载失败: {e}")
        return False

def download_all_models() -> Dict[str, bool]:
    """下载所有腾讯混元模型"""
    results = {}
    for model_size in HUNYUAN_MODELS.keys():
        print(f"\n{'='*60}")
        results[model_size] = download_hunyuan_model(model_size)
    return results

def list_available_models():
    """列出可用的模型"""
    print("📋 可用的腾讯混元模型:")
    print("=" * 60)
    for size, config in HUNYUAN_MODELS.items():
        status = "✅ 已下载" if os.path.exists(config["local_dir"]) else "❌ 未下载"
        print(f"🔸 {size:>4} | {config['memory_requirement']:>4} | {status} | {config['description']}")
    print("=" * 60)

def save_model_config():
    """保存模型配置到文件"""
    config_file = "./models/model_config.json"
    os.makedirs("./models", exist_ok=True)

    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(HUNYUAN_MODELS, f, ensure_ascii=False, indent=2)

    print(f"📄 模型配置已保存到: {config_file}")

def create_model_usage_guide():
    """创建模型使用指南"""
    guide_content = """# 腾讯混元模型使用指南

## 模型选择建议

### 🔸 Hunyuan-0.5B-Instruct (2GB内存)
- **适用场景**: 资源受限环境、快速原型验证
- **优势**: 启动快速、内存占用小
- **适合任务**: 基础Shader结构识别、简单组件提取

### 🔸 Hunyuan-1.8B-Instruct (4GB内存)
- **适用场景**: 日常开发、中等复杂度分析
- **优势**: 性能与资源平衡
- **适合任务**: 常规Shader分析、函数拆解、优化建议

### 🔸 Hunyuan-4B-Instruct (8GB内存)
- **适用场景**: 专业开发、复杂Shader分析
- **优势**: 更好的理解能力和分析精度
- **适合任务**: 复杂Shader重构、高级优化建议

### 🔸 Hunyuan-7B-Instruct (16GB内存)
- **适用场景**: 高端工作站、最佳分析效果
- **优势**: 最强的代码理解和生成能力
- **适合任务**: 复杂Shader原子化、智能重构建议

## 使用方法

### 下载单个模型
```bash
python download_model.py --model 1.8B
```

### 下载所有模型
```bash
python download_model.py --all
```

### 查看已下载模型
```bash
python download_model.py --list
```

### 在分析器中指定模型
```bash
python shader_analyzer.py --input shader.hlsl --model 1.8B --output ./output
```

## 性能对比

| 模型规模 | 内存需求 | 分析速度 | 分析质量 | 推荐场景 |
|---------|---------|---------|---------|---------|
| 0.5B    | 2GB     | ⭐⭐⭐⭐⭐ | ⭐⭐⭐   | 快速验证 |
| 1.8B    | 4GB     | ⭐⭐⭐⭐   | ⭐⭐⭐⭐ | 日常开发 |
| 4B      | 8GB     | ⭐⭐⭐     | ⭐⭐⭐⭐⭐ | 专业分析 |
| 7B      | 16GB    | ⭐⭐       | ⭐⭐⭐⭐⭐ | 最佳效果 |

## 注意事项

1. **内存要求**: 确保系统有足够内存运行选定的模型
2. **GPU加速**: 支持CUDA的GPU可显著提升推理速度
3. **模型切换**: 可在运行时通过参数切换不同规模的模型
4. **量化版本**: 未来将支持INT4/FP8量化版本以降低内存需求
"""

    with open("MODEL_USAGE_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide_content)

    print("📋 已创建模型使用指南: MODEL_USAGE_GUIDE.md")

def main():
    parser = argparse.ArgumentParser(description="腾讯混元模型下载器")
    parser.add_argument("--model", "-m", choices=list(HUNYUAN_MODELS.keys()),
                       help="下载指定规模的模型")
    parser.add_argument("--all", "-a", action="store_true",
                       help="下载所有模型")
    parser.add_argument("--list", "-l", action="store_true",
                       help="列出可用模型")
    parser.add_argument("--force", "-f", action="store_true",
                       help="强制重新下载")

    args = parser.parse_args()

    print("🚀 Unity Shader分析器 - 腾讯混元模型下载器")
    print("=" * 60)

    # 创建模型目录
    os.makedirs("./models", exist_ok=True)

    if args.list:
        list_available_models()
        return

    if args.all:
        print("📦 开始下载所有腾讯混元模型...")
        results = download_all_models()

        print(f"\n{'='*60}")
        print("📊 下载结果汇总:")
        for model_size, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {model_size:>4}: {status}")

    elif args.model:
        print(f"📦 开始下载 {args.model} 模型...")
        success = download_hunyuan_model(args.model, args.force)
        if success:
            print(f"✅ 模型 {args.model} 下载完成")
        else:
            print(f"❌ 模型 {args.model} 下载失败")
    else:
        # 默认行为：显示帮助和可用模型
        list_available_models()
        print("\n💡 使用提示:")
        print("  下载单个模型: python download_model.py --model 1.8B")
        print("  下载所有模型: python download_model.py --all")
        print("  查看帮助:     python download_model.py --help")

    # 保存配置和创建指南
    save_model_config()
    create_model_usage_guide()

    print(f"\n✅ 准备就绪！现在可以运行分析器了")
    print("运行: python shader_analyzer.py --help 查看使用说明")

if __name__ == "__main__":
    main()