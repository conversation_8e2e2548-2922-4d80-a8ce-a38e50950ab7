@echo off
echo Unity Shader原子化拆解工具
echo =============================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未找到，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

REM 运行分析器
if "%1"=="" (
    echo 用法:
    echo   %0 -i shader文件路径 [-o 输出目录]
    echo.
    echo 示例:
    echo   %0 -i example_shaders\basic_shader.shader
    echo   %0 -i example_shaders -o ./output
    pause
    exit /b 0
)

python shader_analyzer.py %*
echo.
echo ✅ 分析完成！
pause