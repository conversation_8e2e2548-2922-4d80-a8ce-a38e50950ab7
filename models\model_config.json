{"0.5B": {"name": "tencent/Hunyuan-0.5B-Instruct", "size": "0.5B", "description": "最小模型，适合资源受限环境", "local_dir": "./models/hunyuan-0.5B-instruct", "memory_requirement": "2GB"}, "1.8B": {"name": "tencent/Hunyuan-1.8B-Instruct", "size": "1.8B", "description": "轻量级模型，平衡性能和资源消耗", "local_dir": "./models/hunyuan-1.8B-instruct", "memory_requirement": "4GB"}, "4B": {"name": "tencent/Hunyuan-4B-Instruct", "size": "4B", "description": "中等规模模型，提供更好的分析能力", "local_dir": "./models/hunyuan-4B-instruct", "memory_requirement": "8GB"}, "7B": {"name": "tencent/Hunyuan-7B-Instruct", "size": "7B", "description": "大规模模型，最佳分析效果", "local_dir": "./models/hunyuan-7B-instruct", "memory_requirement": "16GB"}}