# 腾讯混元模型使用指南

## 模型选择建议

### 🔸 Hunyuan-0.5B-Instruct (2GB内存)
- **适用场景**: 资源受限环境、快速原型验证
- **优势**: 启动快速、内存占用小
- **适合任务**: 基础Shader结构识别、简单组件提取

### 🔸 Hunyuan-1.8B-Instruct (4GB内存)
- **适用场景**: 日常开发、中等复杂度分析
- **优势**: 性能与资源平衡
- **适合任务**: 常规Shader分析、函数拆解、优化建议

### 🔸 Hunyuan-4B-Instruct (8GB内存)
- **适用场景**: 专业开发、复杂Shader分析
- **优势**: 更好的理解能力和分析精度
- **适合任务**: 复杂Shader重构、高级优化建议

### 🔸 Hunyuan-7B-Instruct (16GB内存)
- **适用场景**: 高端工作站、最佳分析效果
- **优势**: 最强的代码理解和生成能力
- **适合任务**: 复杂Shader原子化、智能重构建议

## 使用方法

### 下载单个模型
```bash
python download_model.py --model 1.8B
```

### 下载所有模型
```bash
python download_model.py --all
```

### 查看已下载模型
```bash
python download_model.py --list
```

### 在分析器中指定模型
```bash
python shader_analyzer.py --input shader.hlsl --model 1.8B --output ./output
```

## 性能对比

| 模型规模 | 内存需求 | 分析速度 | 分析质量 | 推荐场景 |
|---------|---------|---------|---------|---------|
| 0.5B    | 2GB     | ⭐⭐⭐⭐⭐ | ⭐⭐⭐   | 快速验证 |
| 1.8B    | 4GB     | ⭐⭐⭐⭐   | ⭐⭐⭐⭐ | 日常开发 |
| 4B      | 8GB     | ⭐⭐⭐     | ⭐⭐⭐⭐⭐ | 专业分析 |
| 7B      | 16GB    | ⭐⭐       | ⭐⭐⭐⭐⭐ | 最佳效果 |

## 注意事项

1. **内存要求**: 确保系统有足够内存运行选定的模型
2. **GPU加速**: 支持CUDA的GPU可显著提升推理速度
3. **模型切换**: 可在运行时通过参数切换不同规模的模型
4. **量化版本**: 未来将支持INT4/FP8量化版本以降低内存需求
