# Unity Shader原子化拆解工具

使用腾讯混元大模型(0.5B/1.8B/4B/7B)实现Unity Shader的智能原子化拆解和分析。

## 🚀 功能特性

- **🤖 智能分析**: 使用腾讯混元模型深度分析shader代码
- **⚛️ 原子化拆解**: 将复杂shader拆分为可重用的原子组件
- **💡 优化建议**: 提供基于AI的shader优化建议
- **📦 批量处理**: 支持单个文件或整个目录的批量分析
- **🔧 组件管理**: 自动生成可重用组件库
- **🔄 模型切换**: 支持多种规模模型，灵活选择性能与资源平衡

## 🎯 支持的模型

| 模型规模 | 内存需求 | 分析速度 | 分析质量 | 推荐场景 |
|---------|---------|---------|---------|---------|
| **Hunyuan-0.5B** | 2GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 快速验证、资源受限环境 |
| **Hunyuan-1.8B** | 4GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 日常开发、平衡性能 |
| **Hunyuan-4B** | 8GB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 专业分析、复杂Shader |
| **Hunyuan-7B** | 16GB | ⭐⭐ | ⭐⭐⭐⭐⭐ | 最佳效果、高端工作站 |

## 📦 快速开始

### 方法1: 一键设置（推荐）

```bash
# Windows
setup_env.bat

# 或手动运行
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

### 方法2: 测试基础功能

```bash
# 测试系统是否正常工作
python test_basic_functionality.py
```

### 方法3: 下载模型

```bash
# 查看可用模型
python download_model.py --list

# 下载推荐模型（平衡性能）
python download_model.py --model 1.8B

# 下载轻量级模型（快速测试）
python download_model.py --model 0.5B

# 下载所有模型
python download_model.py --all
```

## 🔧 使用方法

### 基础使用

```bash
# 分析单个Shader文件
python shader_analyzer.py -i shader.hlsl -o ./output

# 分析整个目录
python shader_analyzer.py -i ./shaders -o ./output

# 使用特定模型
python shader_analyzer.py -i shader.hlsl -m 4B -o ./output

# 查看帮助
python shader_analyzer.py --help
```

### 高级功能

```bash
# 列出可用模型
python shader_analyzer.py --list-models

# 查看当前模型信息
python shader_analyzer.py --model-info

# 详细输出模式
python shader_analyzer.py -i shader.hlsl -v
```

### 参数说明
- `-i, --input`: 输入的shader文件或目录路径
- `-o, --output`: 输出目录（默认: ./output）
- `-m, --model`: 模型规模 (0.5B/1.8B/4B/7B，默认: 1.8B)
- `--models-dir`: 模型存储目录（默认: ./models）
- `-v, --verbose`: 详细输出模式

## 输出结果

### 分析结果文件
每个shader会生成对应的JSON分析文件，包含：
- **属性列表**: shader中定义的所有属性
- **变量分析**: 全局变量、uniform变量的详细分析
- **函数拆解**: 每个函数的功能和复杂度评估
- **Pass分析**: 各个Pass的渲染功能
- **原子组件**: 可重用的代码片段
- **优化建议**: AI提供的优化方案

### 可重用组件库
在 `output/atomic_components/` 目录下：
- 自动提取的可重用函数和代码片段
- 标准化的组件文件格式
- 组件索引文件便于管理

## 示例

### 输入示例
```hlsl
Shader "Custom/MyShader" {
    Properties {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
    }
    
    SubShader {
        Pass {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            struct appdata {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
            
            sampler2D _MainTex;
            float4 _Color;
            
            v2f vert (appdata v) {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target {
                fixed4 col = tex2D(_MainTex, i.uv) * _Color;
                return col;
            }
            ENDCG
        }
    }
}
```

### 输出示例
生成的JSON分析结果将包含：
```json
{
    "shader_name": "MyShader",
    "properties": [
        {
            "name": "_MainTex",
            "type": "2D",
            "description": "主纹理贴图",
            "default_value": "white"
        }
    ],
    "functions": [
        {
            "name": "vert",
            "return_type": "v2f",
            "description": "顶点着色器函数",
            "complexity": "低"
        }
    ],
    "atomic_components": [
        {
            "name": "BasicVertexTransform",
            "type": "顶点变换",
            "code": "o.vertex = UnityObjectToClipPos(v.vertex);",
            "reusable": true
        }
    ]
}
```

## 💻 系统要求

### 基础要求
- **Python**: 3.8或更高版本
- **存储**: 至少5GB可用空间（根据模型选择）
- **操作系统**: Windows 10+, Linux, macOS

### 内存要求（根据模型选择）
- **Hunyuan-0.5B**: 2GB RAM
- **Hunyuan-1.8B**: 4GB RAM
- **Hunyuan-4B**: 8GB RAM
- **Hunyuan-7B**: 16GB RAM

### 可选加速
- **GPU**: 支持CUDA的NVIDIA显卡（推荐）
- **CPU**: 多核处理器（Intel i5+/AMD Ryzen 5+）

## 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 确保能访问Hugging Face
   - 尝试使用代理

2. **内存不足**
   - 确保有至少8GB可用内存
   - 关闭其他占用内存的程序

3. **CUDA错误**
   - 如不使用GPU，可修改代码强制使用CPU
   - 检查CUDA驱动版本

### 技术支持

如遇到问题，请检查：
1. 虚拟环境是否正确激活
2. 所有依赖是否正确安装
3. 模型文件是否完整下载
4. 查看错误日志获取详细信息

## 许可证

本项目仅供学习和研究使用。Gemma模型遵循Google的使用条款。