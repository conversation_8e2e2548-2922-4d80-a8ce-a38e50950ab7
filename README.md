# Unity Shader原子化拆解工具

使用Gemma3n量化版模型(7GB)实现Unity Shader的智能原子化拆解和分析。

## 功能特性

- **智能分析**: 使用Gemma3n模型深度分析shader代码
- **原子化拆解**: 将复杂shader拆分为可重用的原子组件
- **优化建议**: 提供基于AI的shader优化建议
- **批量处理**: 支持单个文件或整个目录的批量分析
- **组件管理**: 自动生成可重用组件库

## 安装和设置

### 方法1: 一键设置（推荐）

双击运行 `setup_env.bat` 自动完成：
1. 创建Python虚拟环境
2. 安装所有依赖库
3. 下载Gemma3n量化版模型

### 方法2: 手动设置

1. 创建虚拟环境：
```bash
python -m venv venv
```

2. 激活虚拟环境：
```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 下载模型：
```bash
python download_model.py
```

## 使用方法

### 分析单个Shader文件
```bash
python shader_analyzer.py -i path/to/your.shader -o ./output
```

### 分析整个目录
```bash
python shader_analyzer.py -i ./shaders_directory -o ./output
```

### 参数说明
- `-i, --input`: 输入的shader文件或目录路径
- `-o, --output`: 输出目录（默认: ./output）
- `-m, --model`: 模型路径（默认: ./models/gemma-3b-it-quantized）

## 输出结果

### 分析结果文件
每个shader会生成对应的JSON分析文件，包含：
- **属性列表**: shader中定义的所有属性
- **变量分析**: 全局变量、uniform变量的详细分析
- **函数拆解**: 每个函数的功能和复杂度评估
- **Pass分析**: 各个Pass的渲染功能
- **原子组件**: 可重用的代码片段
- **优化建议**: AI提供的优化方案

### 可重用组件库
在 `output/atomic_components/` 目录下：
- 自动提取的可重用函数和代码片段
- 标准化的组件文件格式
- 组件索引文件便于管理

## 示例

### 输入示例
```hlsl
Shader "Custom/MyShader" {
    Properties {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
    }
    
    SubShader {
        Pass {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            struct appdata {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
            
            sampler2D _MainTex;
            float4 _Color;
            
            v2f vert (appdata v) {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target {
                fixed4 col = tex2D(_MainTex, i.uv) * _Color;
                return col;
            }
            ENDCG
        }
    }
}
```

### 输出示例
生成的JSON分析结果将包含：
```json
{
    "shader_name": "MyShader",
    "properties": [
        {
            "name": "_MainTex",
            "type": "2D",
            "description": "主纹理贴图",
            "default_value": "white"
        }
    ],
    "functions": [
        {
            "name": "vert",
            "return_type": "v2f",
            "description": "顶点着色器函数",
            "complexity": "低"
        }
    ],
    "atomic_components": [
        {
            "name": "BasicVertexTransform",
            "type": "顶点变换",
            "code": "o.vertex = UnityObjectToClipPos(v.vertex);",
            "reusable": true
        }
    ]
}
```

## 系统要求

- **内存**: 至少8GB RAM（推荐16GB）
- **存储**: 至少10GB可用空间（模型7GB + 缓存）
- **Python**: 3.8或更高版本
- **GPU**: 可选，支持CUDA加速

## 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 确保能访问Hugging Face
   - 尝试使用代理

2. **内存不足**
   - 确保有至少8GB可用内存
   - 关闭其他占用内存的程序

3. **CUDA错误**
   - 如不使用GPU，可修改代码强制使用CPU
   - 检查CUDA驱动版本

### 技术支持

如遇到问题，请检查：
1. 虚拟环境是否正确激活
2. 所有依赖是否正确安装
3. 模型文件是否完整下载
4. 查看错误日志获取详细信息

## 许可证

本项目仅供学习和研究使用。Gemma模型遵循Google的使用条款。